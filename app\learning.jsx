import React, { useState } from 'react';
import { StyleSheet, View, ScrollView, TouchableOpacity, Image, Dimensions } from 'react-native';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import ThemedView from '../components/ThemedView';
import ThemedText from '../components/ThemedText';
import { Colors } from '../constants/Colors';

const { width: screenWidth } = Dimensions.get('window');

const LearningPage = () => {
  const [activeCategory, setActiveCategory] = useState('全部');

  const categories = ['全部', '数学', '语文', '英语', '物理', '化学', '生物'];

  const learningResources = [
    {
      id: 1,
      title: '高中数学函数专题',
      description: '深入理解函数的概念、性质和应用',
      category: '数学',
      duration: '45分钟',
      difficulty: '中等',
      progress: 60,
      image: require('../assets/Book.png'),
      type: 'video',
    },
    {
      id: 2,
      title: '英语语法精讲',
      description: '系统学习英语语法规则和用法',
      category: '英语',
      duration: '30分钟',
      difficulty: '简单',
      progress: 80,
      image: require('../assets/Book.png'),
      type: 'course',
    },
    {
      id: 3,
      title: '物理力学基础',
      description: '牛顿定律和力学原理详解',
      category: '物理',
      duration: '60分钟',
      difficulty: '困难',
      progress: 25,
      image: require('../assets/Book.png'),
      type: 'video',
    },
    {
      id: 4,
      title: '化学元素周期表',
      description: '元素周期表的规律和应用',
      category: '化学',
      duration: '40分钟',
      difficulty: '中等',
      progress: 0,
      image: require('../assets/Book.png'),
      type: 'interactive',
    },
  ];

  const studyPlans = [
    {
      id: 1,
      title: '高考数学冲刺计划',
      subjects: ['数学'],
      duration: '30天',
      progress: 45,
      tasksCompleted: 18,
      totalTasks: 40,
    },
    {
      id: 2,
      title: '英语词汇提升计划',
      subjects: ['英语'],
      duration: '21天',
      progress: 70,
      tasksCompleted: 14,
      totalTasks: 20,
    },
  ];

  const handleBack = () => {
    router.back();
  };

  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case '简单': return '#28a745';
      case '中等': return '#ffc107';
      case '困难': return '#dc3545';
      default: return '#6c757d';
    }
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'video': return '📹';
      case 'course': return '📚';
      case 'interactive': return '🎮';
      default: return '📖';
    }
  };

  const filteredResources = activeCategory === '全部' 
    ? learningResources 
    : learningResources.filter(resource => resource.category === activeCategory);

  const renderResourceCard = (resource) => (
    <TouchableOpacity key={resource.id} style={styles.resourceCard}>
      <View style={styles.cardHeader}>
        <Image source={resource.image} style={styles.resourceImage} />
        <View style={styles.cardInfo}>
          <View style={styles.cardTitleRow}>
            <ThemedText style={styles.resourceTitle}>{resource.title}</ThemedText>
            <ThemedText style={styles.typeIcon}>{getTypeIcon(resource.type)}</ThemedText>
          </View>
          <ThemedText style={styles.resourceDescription} numberOfLines={2}>
            {resource.description}
          </ThemedText>
          <View style={styles.resourceMeta}>
            <View style={styles.metaItem}>
              <ThemedText style={styles.metaLabel}>时长:</ThemedText>
              <ThemedText style={styles.metaValue}>{resource.duration}</ThemedText>
            </View>
            <View style={[styles.difficultyTag, { backgroundColor: getDifficultyColor(resource.difficulty) + '20' }]}>
              <ThemedText style={[styles.difficultyText, { color: getDifficultyColor(resource.difficulty) }]}>
                {resource.difficulty}
              </ThemedText>
            </View>
          </View>
        </View>
      </View>
      
      {resource.progress > 0 && (
        <View style={styles.progressSection}>
          <View style={styles.progressHeader}>
            <ThemedText style={styles.progressLabel}>学习进度</ThemedText>
            <ThemedText style={styles.progressPercent}>{resource.progress}%</ThemedText>
          </View>
          <View style={styles.progressBar}>
            <View style={[styles.progressFill, { width: `${resource.progress}%` }]} />
          </View>
        </View>
      )}
    </TouchableOpacity>
  );

  const renderStudyPlan = (plan) => (
    <TouchableOpacity key={plan.id} style={styles.planCard}>
      <View style={styles.planHeader}>
        <ThemedText style={styles.planTitle}>{plan.title}</ThemedText>
        <ThemedText style={styles.planDuration}>{plan.duration}</ThemedText>
      </View>
      <View style={styles.planProgress}>
        <View style={styles.progressHeader}>
          <ThemedText style={styles.progressLabel}>
            已完成 {plan.tasksCompleted}/{plan.totalTasks} 个任务
          </ThemedText>
          <ThemedText style={styles.progressPercent}>{plan.progress}%</ThemedText>
        </View>
        <View style={styles.progressBar}>
          <View style={[styles.progressFill, { width: `${plan.progress}%` }]} />
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ThemedView style={styles.container}>
        {/* 头部导航 */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <ThemedText style={styles.backText}>← 返回</ThemedText>
          </TouchableOpacity>
          <ThemedText style={styles.headerTitle}>学习</ThemedText>
          <TouchableOpacity style={styles.searchButton}>
            <ThemedText style={styles.searchIcon}>🔍</ThemedText>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* 学习计划 */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <ThemedText style={styles.sectionTitle}>我的学习计划</ThemedText>
              <TouchableOpacity>
                <ThemedText style={styles.moreText}>查看全部</ThemedText>
              </TouchableOpacity>
            </View>
            {studyPlans.map(renderStudyPlan)}
          </View>

          {/* 分类筛选 */}
          <View style={styles.section}>
            <ThemedText style={styles.sectionTitle}>学习资源</ThemedText>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoryContainer}>
              {categories.map((category, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.categoryItem,
                    activeCategory === category && styles.activeCategoryItem
                  ]}
                  onPress={() => setActiveCategory(category)}
                >
                  <ThemedText style={[
                    styles.categoryText,
                    activeCategory === category && styles.activeCategoryText
                  ]}>
                    {category}
                  </ThemedText>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>

          {/* 学习资源列表 */}
          <View style={styles.section}>
            {filteredResources.map(renderResourceCard)}
          </View>

          {/* 学习统计 */}
          <View style={styles.section}>
            <ThemedText style={styles.sectionTitle}>学习统计</ThemedText>
            <View style={styles.statsContainer}>
              <View style={styles.statItem}>
                <ThemedText style={styles.statNumber}>12</ThemedText>
                <ThemedText style={styles.statLabel}>今日学习</ThemedText>
                <ThemedText style={styles.statUnit}>分钟</ThemedText>
              </View>
              <View style={styles.statItem}>
                <ThemedText style={styles.statNumber}>7</ThemedText>
                <ThemedText style={styles.statLabel}>连续天数</ThemedText>
                <ThemedText style={styles.statUnit}>天</ThemedText>
              </View>
              <View style={styles.statItem}>
                <ThemedText style={styles.statNumber}>85</ThemedText>
                <ThemedText style={styles.statLabel}>完成率</ThemedText>
                <ThemedText style={styles.statUnit}>%</ThemedText>
              </View>
            </View>
          </View>
        </ScrollView>
      </ThemedView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  backButton: {
    padding: 5,
  },
  backText: {
    fontSize: 16,
    color: Colors.primary,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  searchButton: {
    padding: 5,
  },
  searchIcon: {
    fontSize: 18,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  moreText: {
    fontSize: 14,
    color: Colors.primary,
  },
  categoryContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  categoryItem: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#fff',
    borderRadius: 20,
    marginRight: 12,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  activeCategoryItem: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  categoryText: {
    fontSize: 14,
    color: '#666',
  },
  activeCategoryText: {
    color: '#fff',
  },
  resourceCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  cardHeader: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  resourceImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 12,
  },
  cardInfo: {
    flex: 1,
  },
  cardTitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  resourceTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  typeIcon: {
    fontSize: 16,
  },
  resourceDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 8,
  },
  resourceMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metaLabel: {
    fontSize: 12,
    color: '#999',
    marginRight: 4,
  },
  metaValue: {
    fontSize: 12,
    color: '#666',
  },
  difficultyTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  difficultyText: {
    fontSize: 12,
    fontWeight: '500',
  },
  progressSection: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f1f3f4',
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressLabel: {
    fontSize: 12,
    color: '#666',
  },
  progressPercent: {
    fontSize: 12,
    color: Colors.primary,
    fontWeight: '600',
  },
  progressBar: {
    height: 4,
    backgroundColor: '#f1f3f4',
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: Colors.primary,
    borderRadius: 2,
  },
  planCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  planHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  planTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  planDuration: {
    fontSize: 12,
    color: '#666',
    backgroundColor: '#f8f9fa',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  planProgress: {
    marginTop: 8,
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.primary,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  statUnit: {
    fontSize: 10,
    color: '#999',
  },
});

export default LearningPage;
