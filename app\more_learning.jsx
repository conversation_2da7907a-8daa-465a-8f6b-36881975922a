import React, { useState } from 'react';
import { StyleSheet, View, ScrollView, TouchableOpacity, Image, Dimensions, TextInput } from 'react-native';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import ThemedView from '../components/ThemedView';
import ThemedText from '../components/ThemedText';
import { Colors } from '../constants/Colors';

const { width: screenWidth } = Dimensions.get('window');

const MoreLearningPage = () => {
  const [studyNote, setStudyNote] = useState('');
  const [selectedTags, setSelectedTags] = useState([]);

  const tags = ['学习笔记', '知识总结', '学习心得', '复习要点'];

  const learningResources = [
    {
      id: 1,
      title: '今日学习计划',
      description: '数学：函数专题练习\n英语：词汇背诵50个\n物理：力学复习',
      time: '今天 09:00',
      type: 'plan',
    },
    {
      id: 2,
      title: '学习笔记分享',
      description: '二次函数的性质总结，包含开口方向、对称轴、顶点坐标等重要知识点...',
      time: '昨天 20:30',
      type: 'note',
    },
    {
      id: 3,
      title: '错题整理',
      description: '整理了本周数学错题，主要集中在函数图像变换和解析几何部分...',
      time: '2天前',
      type: 'error',
    },
  ];

  const handleBack = () => {
    router.back();
  };

  const handleShare = () => {
    if (!studyNote.trim()) {
      alert('请输入学习内容');
      return;
    }
    
    alert('分享成功！');
    setStudyNote('');
    setSelectedTags([]);
  };

  const toggleTag = (tag) => {
    if (selectedTags.includes(tag)) {
      setSelectedTags(selectedTags.filter(t => t !== tag));
    } else {
      setSelectedTags([...selectedTags, tag]);
    }
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'plan': return '📅';
      case 'note': return '📝';
      case 'error': return '❌';
      default: return '📖';
    }
  };

  const renderLearningItem = (item) => (
    <TouchableOpacity key={item.id} style={styles.learningItem}>
      <View style={styles.itemHeader}>
        <View style={styles.typeContainer}>
          <ThemedText style={styles.typeIcon}>{getTypeIcon(item.type)}</ThemedText>
          <ThemedText style={styles.itemTitle}>{item.title}</ThemedText>
        </View>
        <ThemedText style={styles.timeText}>{item.time}</ThemedText>
      </View>
      <ThemedText style={styles.itemDescription} numberOfLines={3}>
        {item.description}
      </ThemedText>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ThemedView style={styles.container}>
        {/* 头部导航 */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <Image source={require('../assets/Arrows_left.png')} style={styles.backIcon} />
          </TouchableOpacity>
          <ThemedText style={styles.headerTitle}>学习</ThemedText>
          <TouchableOpacity onPress={handleShare} style={styles.shareButton}>
            <ThemedText style={styles.shareText}>分享</ThemedText>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* 白色内容盒子 */}
          <View style={styles.contentBox}>
            {/* 学习内容输入区域 */}
            <View style={styles.inputSection}>
              <TextInput
                style={styles.studyInput}
                placeholder="分享你的学习内容......"
                value={studyNote}
                onChangeText={setStudyNote}
                multiline
                textAlignVertical="top"
                maxLength={200}
              />

              {/* 添加图片按钮和字数统计 */}
              <View style={styles.bottomRow}>
                <TouchableOpacity style={styles.addImageButton}>
                  <Image source={require('../assets/More_image/More.png')} style={styles.addImageIcon} />
                </TouchableOpacity>

                <View style={styles.charCountContainer}>
                  <ThemedText style={styles.charCount}>{studyNote.length}/200</ThemedText>
                </View>
              </View>
            </View>

            {/* 标签选择 */}
            <View style={styles.tagsSection}>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.tagsContainer}>
                {tags.map((tag, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.tagItem,
                      selectedTags.includes(tag) && styles.selectedTag
                    ]}
                    onPress={() => toggleTag(tag)}
                  >
                    <Image source={require('../assets/More_image/Well_number.png')} style={styles.tagPrefix} />
                    <ThemedText style={[
                      styles.tagText,
                      selectedTags.includes(tag) && styles.selectedTagText
                    ]}>
                      {tag}
                    </ThemedText>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          </View>

          {/* 学习动态 */}
          <View style={styles.section}>
            <ThemedText style={styles.sectionTitle}>学习动态</ThemedText>
            {learningResources.map(renderLearningItem)}
          </View>

          {/* 学习统计卡片 */}
          <View style={styles.statsCard}>
            <ThemedText style={styles.statsTitle}>今日学习统计</ThemedText>
            <View style={styles.statsRow}>
              <View style={styles.statItem}>
                <ThemedText style={styles.statNumber}>2.5</ThemedText>
                <ThemedText style={styles.statLabel}>小时</ThemedText>
              </View>
              <View style={styles.statItem}>
                <ThemedText style={styles.statNumber}>8</ThemedText>
                <ThemedText style={styles.statLabel}>任务</ThemedText>
              </View>
              <View style={styles.statItem}>
                <ThemedText style={styles.statNumber}>95%</ThemedText>
                <ThemedText style={styles.statLabel}>完成率</ThemedText>
              </View>
            </View>
          </View>
        </ScrollView>

        {/* 底部装饰花朵图案 */}
        <View style={styles.bottomDecoration}>
          <Image 
            source={require('../assets/More_image/flower.png')} 
            style={styles.flowerPattern}
            resizeMode="cover"
          />
        </View>
      </ThemedView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF5F0',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: 'transparent',
  },
  backButton: {
    padding: 5,
  },
  backIcon: {
    width: 24,
    height: 24,
    tintColor: '#666',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  shareButton: {
    backgroundColor: '#FFB366',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
  },
  shareText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  contentBox: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    marginTop: 20,
    marginBottom: 40,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  inputSection: {
    marginTop: 20,
    marginBottom: 30,
  },
  studyInput: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
    minHeight: 120,
    textAlignVertical: 'top',
    marginBottom: 20,
  },
  bottomRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  addImageButton: {
    width: 80,
    height: 80,
    backgroundColor: '#F5E6D3',
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  addImageIcon: {
    width: 24,
    height: 24,
    tintColor: '#D4A574',
  },
  charCountContainer: {
    alignItems: 'flex-end',
  },
  charCount: {
    fontSize: 14,
    color: '#999',
  },
  tagsSection: {
    marginBottom: 30,
  },
  tagsContainer: {
    flexDirection: 'row',
  },
  tagItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFEEDB',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 12,
  },
  selectedTag: {
    backgroundColor: '#FFD29B',
  },
  tagPrefix: {
    width: 16,
    height: 16,
    tintColor: '#fff',
    marginRight: 4,
  },
  tagText: {
    fontSize: 14,
    color: '#fff',
    fontWeight: '500',
  },
  selectedTagText: {
    color: '#fff',
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  learningItem: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  typeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  typeIcon: {
    fontSize: 16,
    marginRight: 8,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  timeText: {
    fontSize: 12,
    color: '#999',
  },
  itemDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  statsCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 40,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  statsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFB366',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
  },
  bottomDecoration: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 120,
    overflow: 'hidden',
  },
  flowerPattern: {
    width: '100%',
    height: '100%',
    opacity: 0.6,
  },
});

export default MoreLearningPage;
