import React, { useState } from 'react';
import { StyleSheet, View, ScrollView, TouchableOpacity, TextInput, Image } from 'react-native';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import ThemedView from '../components/ThemedView';
import ThemedText from '../components/ThemedText';
import { Colors } from '../constants/Colors';

const MoreQAPage = () => {
  const [activeTab, setActiveTab] = useState('提问');
  const [question, setQuestion] = useState('');
  const [selectedTags, setSelectedTags] = useState([]);

  const tags = ['数学难题', '英语语法', '物理实验', '化学反应'];

  const mockQuestions = [
    {
      id: 1,
      title: '关于二次函数的最值问题',
      content: '如何求解二次函数在给定区间内的最值？',
      subject: '数学',
      time: '2小时前',
      answers: 3,
      isAnswered: true,
    },
    {
      id: 2,
      title: '英语语法中的虚拟语气',
      content: '虚拟语气在不同时态下的用法有什么区别？',
      subject: '英语',
      time: '5小时前',
      answers: 1,
      isAnswered: false,
    },
  ];

  const handleBack = () => {
    router.back();
  };

  const handleSubmitQuestion = () => {
    if (!question.trim()) {
      alert('请输入问题内容');
      return;
    }
    
    alert('问题提交成功！');
    setQuestion('');
  };

  const toggleTag = (tag) => {
    if (selectedTags.includes(tag)) {
      setSelectedTags(selectedTags.filter(t => t !== tag));
    } else {
      setSelectedTags([...selectedTags, tag]);
    }
  };

  const renderQuestionItem = (item) => (
    <TouchableOpacity key={item.id} style={styles.questionItem}>
      <View style={styles.questionHeader}>
        <View style={styles.subjectTag}>
          <ThemedText style={styles.subjectText}>{item.subject}</ThemedText>
        </View>
        <View style={[styles.statusTag, item.isAnswered && styles.answeredTag]}>
          <ThemedText style={[styles.statusText, item.isAnswered && styles.answeredText]}>
            {item.isAnswered ? '已解答' : '待解答'}
          </ThemedText>
        </View>
      </View>
      <ThemedText style={styles.questionTitle}>{item.title}</ThemedText>
      <ThemedText style={styles.questionContent} numberOfLines={2}>{item.content}</ThemedText>
      <View style={styles.questionFooter}>
        <ThemedText style={styles.timeText}>{item.time}</ThemedText>
        <ThemedText style={styles.answerCount}>{item.answers} 个回答</ThemedText>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ThemedView style={styles.container}>
        {/* 头部导航 */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <Image source={require('../assets/Arrows_left.png')} style={styles.backIcon} />
          </TouchableOpacity>
          <ThemedText style={styles.headerTitle}>答疑</ThemedText>
          <View style={styles.placeholder} />
        </View>

        {/* 标签切换 */}
        <View style={styles.tabContainer}>
          {['提问', '回答'].map((tab) => (
            <TouchableOpacity
              key={tab}
              style={[styles.tab, activeTab === tab && styles.activeTab]}
              onPress={() => setActiveTab(tab)}
            >
              <ThemedText style={[styles.tabText, activeTab === tab && styles.activeTabText]}>
                {tab}
              </ThemedText>
            </TouchableOpacity>
          ))}
        </View>

        {activeTab === '提问' ? (
          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            {/* 白色内容盒子 - 只包含输入区域 */}
            <View style={styles.contentBox}>
              <TextInput
                style={styles.questionInput}
                placeholder="描述你的问题......"
                value={question}
                onChangeText={setQuestion}
                multiline
                textAlignVertical="top"
                maxLength={200}
              />

              {/* 添加图片按钮和字数统计 */}
              <View style={styles.bottomRow}>
                <TouchableOpacity style={styles.addImageButton}>
                  <Image source={require('../assets/More_image/More.png')} style={styles.addImageIcon} />
                </TouchableOpacity>

                <View style={styles.charCountContainer}>
                  <ThemedText style={styles.charCount}>{question.length}/200</ThemedText>
                </View>
              </View>
            </View>

            {/* 标签选择 */}
            <View style={styles.tagsSection}>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.tagsContainer}>
                {tags.map((tag, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.tagItem,
                      selectedTags.includes(tag) && styles.selectedTag
                    ]}
                    onPress={() => toggleTag(tag)}
                  >
                    <Image source={require('../assets/More_image/Well_number.png')} style={styles.tagPrefix} />
                    <ThemedText style={[
                      styles.tagText,
                      selectedTags.includes(tag) && styles.selectedTagText
                    ]}>
                      {tag}
                    </ThemedText>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            <TouchableOpacity style={styles.submitButton} onPress={handleSubmitQuestion}>
              <ThemedText style={styles.submitText}>提交问题</ThemedText>
            </TouchableOpacity>
          </ScrollView>
        ) : (
          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            {/* 问题列表 */}
            <View style={styles.section}>
              <ThemedText style={styles.sectionTitle}>待回答问题</ThemedText>
              {mockQuestions.map(renderQuestionItem)}
            </View>
          </ScrollView>
        )}

        {/* 底部装饰花朵图案 */}
        <View style={styles.bottomDecoration}>
          <Image 
            source={require('../assets/More_image/flower.png')} 
            style={styles.flowerPattern}
            resizeMode="cover"
          />
        </View>
      </ThemedView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF5F0',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: 'transparent',
  },
  backButton: {
    padding: 5,
  },
  backIcon: {
    width: 24,
    height: 24,
    tintColor: '#666',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  placeholder: {
    width: 40,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: 'transparent',
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 10,
    borderRadius: 20,
    marginHorizontal: 5,
    backgroundColor: '#F5E6D3',
  },
  activeTab: {
    backgroundColor: '#FFB366',
  },
  tabText: {
    fontSize: 14,
    color: '#D4A574',
  },
  activeTabText: {
    color: '#fff',
    fontWeight: '500',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  contentBox: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    marginTop: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  questionInput: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
    minHeight: 120,
    textAlignVertical: 'top',
    marginBottom: 20,
  },
  bottomRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  addImageButton: {
    width: 80,
    height: 80,
    backgroundColor: '#F5E6D3',
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  addImageIcon: {
    width: 24,
    height: 24,
    tintColor: '#D4A574',
  },
  charCountContainer: {
    alignItems: 'flex-end',
  },
  charCount: {
    fontSize: 14,
    color: '#999',
  },
  tagsSection: {
    marginBottom: 30,
  },
  tagsContainer: {
    flexDirection: 'row',
  },
  tagItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFEEDB',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 12,
  },
  selectedTag: {
    backgroundColor: '#FFD29B',
  },
  tagPrefix: {
    width: 16,
    height: 16,
    tintColor: '#fff',
    marginRight: 4,
  },
  tagText: {
    fontSize: 14,
    color: '#fff',
    fontWeight: '500',
  },
  selectedTagText: {
    color: '#fff',
  },
  submitButton: {
    backgroundColor: '#FFC47B',
    borderRadius: 20,
    paddingVertical: 15,
    alignItems: 'center',
    marginBottom: 40,
  },
  submitText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    color: '#333',
  },
  questionItem: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  questionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  subjectTag: {
    backgroundColor: '#F5E6D3',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  subjectText: {
    fontSize: 12,
    color: '#D4A574',
  },
  statusTag: {
    backgroundColor: '#fff3cd',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  answeredTag: {
    backgroundColor: '#d1edff',
  },
  statusText: {
    fontSize: 12,
    color: '#856404',
  },
  answeredText: {
    color: '#0c5460',
  },
  questionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333',
  },
  questionContent: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 12,
  },
  questionFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  timeText: {
    fontSize: 12,
    color: '#999',
  },
  answerCount: {
    fontSize: 12,
    color: '#FFB366',
  },
  bottomDecoration: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 120,
    overflow: 'hidden',
  },
  flowerPattern: {
    width: '100%',
    height: '100%',
    opacity: 0.6,
  },
});

export default MoreQAPage;
