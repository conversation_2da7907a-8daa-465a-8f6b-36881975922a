import React, { useState } from 'react';
import { StyleSheet, View, ScrollView, TouchableOpacity, TextInput, Image } from 'react-native';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import ThemedView from '../components/ThemedView';
import ThemedText from '../components/ThemedText';
import { Colors } from '../constants/Colors';

const QAPage = () => {
  const [activeTab, setActiveTab] = useState('提问');
  const [question, setQuestion] = useState('');
  const [selectedSubject, setSelectedSubject] = useState('数学');

  const subjects = ['数学', '语文', '英语', '物理', '化学', '生物', '历史', '地理'];
  
  const mockQuestions = [
    {
      id: 1,
      title: '关于二次函数的最值问题',
      content: '如何求解二次函数在给定区间内的最值？',
      subject: '数学',
      time: '2小时前',
      answers: 3,
      isAnswered: true,
    },
    {
      id: 2,
      title: '英语语法中的虚拟语气',
      content: '虚拟语气在不同时态下的用法有什么区别？',
      subject: '英语',
      time: '5小时前',
      answers: 1,
      isAnswered: false,
    },
    {
      id: 3,
      title: '化学反应平衡常数',
      content: '温度对化学平衡常数有什么影响？',
      subject: '化学',
      time: '1天前',
      answers: 5,
      isAnswered: true,
    },
  ];

  const handleBack = () => {
    router.back();
  };

  const handleSubmitQuestion = () => {
    if (!question.trim()) {
      alert('请输入问题内容');
      return;
    }
    
    // 这里可以添加提交问题的API调用
    alert('问题提交成功！');
    setQuestion('');
  };

  const renderQuestionItem = (item) => (
    <TouchableOpacity key={item.id} style={styles.questionItem}>
      <View style={styles.questionHeader}>
        <View style={styles.subjectTag}>
          <ThemedText style={styles.subjectText}>{item.subject}</ThemedText>
        </View>
        <View style={[styles.statusTag, item.isAnswered && styles.answeredTag]}>
          <ThemedText style={[styles.statusText, item.isAnswered && styles.answeredText]}>
            {item.isAnswered ? '已解答' : '待解答'}
          </ThemedText>
        </View>
      </View>
      <ThemedText style={styles.questionTitle}>{item.title}</ThemedText>
      <ThemedText style={styles.questionContent} numberOfLines={2}>{item.content}</ThemedText>
      <View style={styles.questionFooter}>
        <ThemedText style={styles.timeText}>{item.time}</ThemedText>
        <ThemedText style={styles.answerCount}>{item.answers} 个回答</ThemedText>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ThemedView style={styles.container}>
        {/* 头部导航 */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <ThemedText style={styles.backText}>← 返回</ThemedText>
          </TouchableOpacity>
          <ThemedText style={styles.headerTitle}>答疑</ThemedText>
          <View style={styles.placeholder} />
        </View>

        {/* 标签切换 */}
        <View style={styles.tabContainer}>
          {['提问', '回答'].map((tab) => (
            <TouchableOpacity
              key={tab}
              style={[styles.tab, activeTab === tab && styles.activeTab]}
              onPress={() => setActiveTab(tab)}
            >
              <ThemedText style={[styles.tabText, activeTab === tab && styles.activeTabText]}>
                {tab}
              </ThemedText>
            </TouchableOpacity>
          ))}
        </View>

        {activeTab === '提问' ? (
          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            {/* 科目选择 */}
            <View style={styles.section}>
              <ThemedText style={styles.sectionTitle}>选择科目</ThemedText>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.subjectContainer}>
                {subjects.map((subject, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.subjectItem,
                      selectedSubject === subject && styles.selectedSubject
                    ]}
                    onPress={() => setSelectedSubject(subject)}
                  >
                    <ThemedText style={[
                      styles.subjectItemText,
                      selectedSubject === subject && styles.selectedSubjectText
                    ]}>
                      {subject}
                    </ThemedText>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            {/* 问题输入 */}
            <View style={styles.section}>
              <ThemedText style={styles.sectionTitle}>描述你的问题</ThemedText>
              <TextInput
                style={styles.questionInput}
                placeholder="详细描述你遇到的问题，包括具体的题目或概念..."
                value={question}
                onChangeText={setQuestion}
                multiline
                textAlignVertical="top"
                maxLength={500}
              />
              <ThemedText style={styles.charCount}>{question.length}/500</ThemedText>
              
              <TouchableOpacity style={styles.submitButton} onPress={handleSubmitQuestion}>
                <ThemedText style={styles.submitText}>提交问题</ThemedText>
              </TouchableOpacity>
            </View>

            {/* 提问技巧 */}
            <View style={styles.tipSection}>
              <ThemedText style={styles.tipTitle}>提问技巧</ThemedText>
              <ThemedText style={styles.tipText}>• 清楚描述问题的背景和具体内容</ThemedText>
              <ThemedText style={styles.tipText}>• 如果是题目，请提供完整的题目信息</ThemedText>
              <ThemedText style={styles.tipText}>• 说明你已经尝试过的解决方法</ThemedText>
              <ThemedText style={styles.tipText}>• 选择正确的科目分类</ThemedText>
            </View>
          </ScrollView>
        ) : (
          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            {/* 问题列表 */}
            <View style={styles.section}>
              <ThemedText style={styles.sectionTitle}>待回答问题</ThemedText>
              {mockQuestions.map(renderQuestionItem)}
            </View>
          </ScrollView>
        )}
      </ThemedView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  backButton: {
    padding: 5,
  },
  backText: {
    fontSize: 16,
    color: Colors.primary,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  placeholder: {
    width: 60,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 10,
    borderRadius: 20,
    marginHorizontal: 5,
  },
  activeTab: {
    backgroundColor: Colors.primary,
  },
  tabText: {
    fontSize: 14,
    color: '#666',
  },
  activeTabText: {
    color: '#fff',
    fontWeight: '500',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    color: '#333',
  },
  subjectContainer: {
    flexDirection: 'row',
  },
  subjectItem: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#fff',
    borderRadius: 20,
    marginRight: 12,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  selectedSubject: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  subjectItemText: {
    fontSize: 14,
    color: '#666',
  },
  selectedSubjectText: {
    color: '#fff',
  },
  questionInput: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#e9ecef',
    height: 150,
    marginBottom: 8,
  },
  charCount: {
    fontSize: 12,
    color: '#999',
    textAlign: 'right',
    marginBottom: 16,
  },
  submitButton: {
    backgroundColor: Colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  submitText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  questionItem: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  questionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  subjectTag: {
    backgroundColor: '#f8f9fa',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  subjectText: {
    fontSize: 12,
    color: '#666',
  },
  statusTag: {
    backgroundColor: '#fff3cd',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  answeredTag: {
    backgroundColor: '#d1edff',
  },
  statusText: {
    fontSize: 12,
    color: '#856404',
  },
  answeredText: {
    color: '#0c5460',
  },
  questionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333',
  },
  questionContent: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 12,
  },
  questionFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  timeText: {
    fontSize: 12,
    color: '#999',
  },
  answerCount: {
    fontSize: 12,
    color: Colors.primary,
  },
  tipSection: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginTop: 20,
  },
  tipTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333',
  },
  tipText: {
    fontSize: 12,
    color: '#666',
    lineHeight: 18,
    marginBottom: 4,
  },
});

export default QAPage;
