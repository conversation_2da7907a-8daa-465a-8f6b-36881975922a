import React, { useState } from 'react';
import { StyleSheet, View, ScrollView, TouchableOpacity, TextInput, Alert, Image } from 'react-native';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import ThemedView from '../components/ThemedView';
import ThemedText from '../components/ThemedText';
import { Colors } from '../constants/Colors';

const MorePostingPage = () => {
  const [content, setContent] = useState('');
  const [selectedTags, setSelectedTags] = useState([]);
  const [isPublic, setIsPublic] = useState(true);
  const [friendsOnly, setFriendsOnly] = useState(false);

  const tags = ['我的搭子', '自习室', '职通车', '互助'];

  const handleBack = () => {
    router.back();
  };

  const handlePublish = () => {
    if (!content.trim()) {
      Alert.alert('提示', '请输入内容');
      return;
    }
    
    Alert.alert('成功', '发布成功！', [
      { text: '确定', onPress: () => router.back() }
    ]);
  };

  const toggleTag = (tag) => {
    if (selectedTags.includes(tag)) {
      setSelectedTags(selectedTags.filter(t => t !== tag));
    } else {
      setSelectedTags([...selectedTags, tag]);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ThemedView style={styles.container}>
        {/* 头部导航 */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <Image source={require('../assets/Arrows_left.png')} style={styles.backIcon} />
          </TouchableOpacity>
          <TouchableOpacity onPress={handlePublish} style={styles.publishButton}>
            <ThemedText style={styles.publishText}>发布</ThemedText>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* 白色内容盒子 - 只包含输入区域 */}
          <View style={styles.contentBox}>
            {/* 内容输入区域 */}
            <TextInput
              style={styles.contentInput}
              placeholder="分享新鲜事......"
              value={content}
              onChangeText={setContent}
              multiline
              textAlignVertical="top"
              maxLength={200}
            />

            {/* 添加图片按钮和字数统计 */}
            <View style={styles.bottomRow}>
              <TouchableOpacity style={styles.addImageButton}>
                <Image source={require('../assets/More_image/More.png')} style={styles.addImageIcon} />
              </TouchableOpacity>

              <View style={styles.charCountContainer}>
                <ThemedText style={styles.charCount}>{content.length}/200</ThemedText>
              </View>
            </View>
          </View>

          {/* 标签选择 */}
          <View style={styles.tagsSection}>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.tagsContainer}>
              {tags.map((tag, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.tagItem,
                    selectedTags.includes(tag) && styles.selectedTag
                  ]}
                  onPress={() => toggleTag(tag)}
                >
                  <Image
                    source={require('../assets/More_image/Well_number.png')}
                    style={selectedTags.includes(tag) ? styles.selectedTagPrefix : styles.tagPrefix}
                  />
                  <ThemedText style={[
                    styles.tagText,
                    selectedTags.includes(tag) && styles.selectedTagText
                  ]}>
                    {tag}
                  </ThemedText>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>

          {/* 可见性设置 */}
          <View style={styles.visibilitySection}>
            <TouchableOpacity
              style={styles.visibilityOption}
              onPress={() => {
                setIsPublic(true);
                setFriendsOnly(false);
              }}
            >
              <View style={[styles.checkbox, isPublic && styles.checkedBox]}>
                {isPublic && <ThemedText style={styles.checkmark}>✓</ThemedText>}
              </View>
              <ThemedText style={styles.visibilityText}>公开</ThemedText>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.visibilityOption}
              onPress={() => {
                setIsPublic(false);
                setFriendsOnly(true);
              }}
            >
              <View style={[styles.checkbox, friendsOnly && styles.checkedBox]}>
                {friendsOnly && <ThemedText style={styles.checkmark}>✓</ThemedText>}
              </View>
              <ThemedText style={styles.visibilityText}>仅好友可见</ThemedText>
            </TouchableOpacity>
          </View>
        </ScrollView>

        {/* 底部装饰花朵图案 */}
        <View style={styles.bottomDecoration}>
          <Image 
            source={require('../assets/More_image/flower.png')} 
            style={styles.flowerPattern}
            resizeMode="cover"
          />
        </View>
      </ThemedView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF5F0',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: 'transparent',
  },
  backButton: {
    padding: 5,
  },
  backIcon: {
    width: 24,
    height: 24,
    tintColor: '#666',
  },
  publishButton: {
    backgroundColor: '#FFC47B',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
  },
  publishText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  contentBox: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    marginTop: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  contentInput: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
    minHeight: 120,
    textAlignVertical: 'top',
    marginBottom: 20,
  },
  bottomRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  addImageButton: {
    width: 80,
    height: 80,
    backgroundColor: '#F5E6D3',
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  addImageIcon: {
    width: 24,
    height: 24,
    tintColor: '#D4A574',
  },
  charCountContainer: {
    alignItems: 'flex-end',
  },
  charCount: {
    fontSize: 14,
    color: '#999',
  },
  tagsSection: {
    marginBottom: 20,
  },
  tagsContainer: {
    flexDirection: 'row',
  },
  tagItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFEEDB',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 12,
  },
  selectedTag: {
    backgroundColor: '#FFD29B',
  },
  tagPrefix: {
    width: 16,
    height: 16,
    tintColor: '#FFB87E',
    marginRight: 4,
  },
  selectedTagPrefix: {
    width: 16,
    height: 16,
    tintColor: '#7A3C10',
    marginRight: 4,
  },
  tagText: {
    fontSize: 14,
    color: '#FFB87E',
    fontWeight: '500',
  },
  selectedTagText: {
    color: '#7A3C10',
  },
  visibilitySection: {
    marginBottom: 40,
  },
  visibilityOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: '#ddd',
    borderRadius: 4,
    marginRight: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkedBox: {
    backgroundColor: '#FFB366',
    borderColor: '#FFB366',
  },
  checkmark: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  visibilityText: {
    fontSize: 16,
    color: '#333',
  },
  bottomDecoration: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 120,
    overflow: 'hidden',
  },
  flowerPattern: {
    width: '100%',
    height: '100%',
    opacity: 0.6,
  },
});

export default MorePostingPage;
