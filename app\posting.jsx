import React, { useState } from 'react';
import { StyleSheet, View, ScrollView, TouchableOpacity, TextInput, Alert } from 'react-native';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import ThemedView from '../components/ThemedView';
import ThemedText from '../components/ThemedText';
import { Colors } from '../constants/Colors';

const PostingPage = () => {
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('学习讨论');

  const categories = ['学习讨论', '问题求助', '经验分享', '资源推荐', '其他'];

  const handleBack = () => {
    router.back();
  };

  const handlePublish = () => {
    if (!title.trim() || !content.trim()) {
      Alert.alert('提示', '请填写标题和内容');
      return;
    }
    
    // 这里可以添加发帖的API调用
    Alert.alert('成功', '帖子发布成功！', [
      { text: '确定', onPress: () => router.back() }
    ]);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ThemedView style={styles.container}>
        {/* 头部导航 */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <ThemedText style={styles.backText}>← 返回</ThemedText>
          </TouchableOpacity>
          <ThemedText style={styles.headerTitle}>发帖</ThemedText>
          <TouchableOpacity onPress={handlePublish} style={styles.publishButton}>
            <ThemedText style={styles.publishText}>发布</ThemedText>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* 分类选择 */}
          <View style={styles.section}>
            <ThemedText style={styles.sectionTitle}>选择分类</ThemedText>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoryContainer}>
              {categories.map((category, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.categoryItem,
                    selectedCategory === category && styles.selectedCategory
                  ]}
                  onPress={() => setSelectedCategory(category)}
                >
                  <ThemedText style={[
                    styles.categoryText,
                    selectedCategory === category && styles.selectedCategoryText
                  ]}>
                    {category}
                  </ThemedText>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>

          {/* 标题输入 */}
          <View style={styles.section}>
            <ThemedText style={styles.sectionTitle}>标题</ThemedText>
            <TextInput
              style={styles.titleInput}
              placeholder="请输入帖子标题..."
              value={title}
              onChangeText={setTitle}
              maxLength={50}
            />
            <ThemedText style={styles.charCount}>{title.length}/50</ThemedText>
          </View>

          {/* 内容输入 */}
          <View style={styles.section}>
            <ThemedText style={styles.sectionTitle}>内容</ThemedText>
            <TextInput
              style={styles.contentInput}
              placeholder="分享你的想法、问题或经验..."
              value={content}
              onChangeText={setContent}
              multiline
              textAlignVertical="top"
              maxLength={1000}
            />
            <ThemedText style={styles.charCount}>{content.length}/1000</ThemedText>
          </View>

          {/* 发布提示 */}
          <View style={styles.tipSection}>
            <ThemedText style={styles.tipTitle}>发帖须知</ThemedText>
            <ThemedText style={styles.tipText}>• 请遵守社区规范，文明发言</ThemedText>
            <ThemedText style={styles.tipText}>• 内容应与学习相关，避免无关话题</ThemedText>
            <ThemedText style={styles.tipText}>• 禁止发布广告、垃圾信息</ThemedText>
          </View>
        </ScrollView>
      </ThemedView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  backButton: {
    padding: 5,
  },
  backText: {
    fontSize: 16,
    color: Colors.primary,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  publishButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  publishText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    color: '#333',
  },
  categoryContainer: {
    flexDirection: 'row',
  },
  categoryItem: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#fff',
    borderRadius: 20,
    marginRight: 12,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  selectedCategory: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  categoryText: {
    fontSize: 14,
    color: '#666',
  },
  selectedCategoryText: {
    color: '#fff',
  },
  titleInput: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#e9ecef',
    marginBottom: 8,
  },
  contentInput: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#e9ecef',
    height: 200,
    marginBottom: 8,
  },
  charCount: {
    fontSize: 12,
    color: '#999',
    textAlign: 'right',
  },
  tipSection: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginTop: 20,
  },
  tipTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333',
  },
  tipText: {
    fontSize: 12,
    color: '#666',
    lineHeight: 18,
    marginBottom: 4,
  },
});

export default PostingPage;
